"""Visual PDF processing module for document analysis with image extraction."""

import os
import json
import uuid
import base64
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from kg_agents.config import config_manager


class VisualPDFProcessor:
    """Handles PDF processing with visual content extraction and analysis."""
    
    def __init__(self, raw_folder: str = "subprocess_raw_data/raw", staging_folder: str = "subprocess_raw_data/staging"):
        """
        Initialize the visual PDF processor.
        
        Args:
            raw_folder: Path to the folder containing raw PDF files
            staging_folder: Path to the folder where visual chunks will be stored
        """
        self.raw_folder = Path(raw_folder)
        self.staging_folder = Path(staging_folder)
        self.visuals_folder = Path("subprocess_raw_data/visuals")
        
        # Ensure folders exist
        self.raw_folder.mkdir(parents=True, exist_ok=True)
        self.staging_folder.mkdir(parents=True, exist_ok=True)
        self.visuals_folder.mkdir(parents=True, exist_ok=True)
        
        # Counter for unique folder naming
        self._folder_counter = self._get_next_folder_counter()
        self._visual_ref_counter = 1
    
    def _get_next_folder_counter(self) -> int:
        """Get the next available folder counter."""
        existing_folders = [
            folder for folder in self.staging_folder.iterdir() 
            if folder.is_dir() and folder.name.startswith("chunks_")
        ]
        
        if not existing_folders:
            return 1
        
        # Extract numbers from existing folder names
        numbers = []
        for folder in existing_folders:
            try:
                # Extract number from "chunks_n_name_files" format
                parts = folder.name.split("_")
                if len(parts) >= 2 and parts[0] == "chunks":
                    numbers.append(int(parts[1]))
            except (ValueError, IndexError):
                continue
        
        return max(numbers) + 1 if numbers else 1
    
    def extract_page_elements(self, pdf_path: Path) -> List[Dict[str, Any]]:
        """
        Extract text blocks and images from each page of a PDF.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            List of page data with text blocks and images
        """
        pages_data = []
        doc = None

        try:
            doc = fitz.open(pdf_path)
            print(f"Successfully opened PDF: {pdf_path}")
            print(f"PDF has {len(doc)} pages")

            for page_num in range(len(doc)):
                try:
                    page = doc[page_num]
                    print(f"Processing page {page_num + 1}/{len(doc)}")
                except Exception as page_error:
                    print(f"Error accessing page {page_num}: {page_error}")
                    continue
                
                # Extract text blocks with coordinates
                text_blocks = []
                try:
                    blocks = page.get_text("dict")["blocks"]

                    for block in blocks:
                        try:
                            if "lines" in block:  # Text block
                                bbox = block["bbox"]
                                text_content = ""
                                for line in block["lines"]:
                                    for span in line["spans"]:
                                        text_content += span["text"] + " "

                                if text_content.strip():
                                    text_blocks.append({
                                        "bbox": bbox,
                                        "text": text_content.strip(),
                                        "type": "text"
                                    })
                        except Exception as block_error:
                            print(f"Warning: Could not process text block on page {page_num}: {block_error}")
                            continue

                except Exception as text_error:
                    print(f"Warning: Could not extract text from page {page_num}: {text_error}")
                    text_blocks = []
                
                # Extract images with coordinates
                images = []
                try:
                    image_list = page.get_images(full=True)

                    for img_index, img in enumerate(image_list):
                        try:
                            # Get image bbox - handle different PyMuPDF versions
                            try:
                                img_rect = page.get_image_bbox(img)
                            except:
                                # Fallback: try to get image rectangle from the image reference
                                xref = img[0]
                                img_dict = doc.extract_image(xref)
                                # Use page dimensions as fallback bbox
                                img_rect = fitz.Rect(0, 0, page.rect.width, page.rect.height)

                            # Extract image data
                            xref = img[0]
                            try:
                                # Try modern method first
                                img_dict = doc.extract_image(xref)
                                img_data = img_dict["image"]
                            except:
                                # Fallback to older method
                                pix = fitz.Pixmap(doc, xref)
                                if pix.n - pix.alpha < 4:  # GRAY or RGB
                                    img_data = pix.tobytes("png")
                                else:
                                    pix = None
                                    continue
                                pix = None  # Free memory

                            if img_data:
                                images.append({
                                    "bbox": list(img_rect),
                                    "image_data": img_data,
                                    "type": "image",
                                    "page_num": page_num,
                                    "img_index": img_index
                                })

                        except Exception as img_error:
                            print(f"Warning: Could not extract image {img_index} from page {page_num}: {img_error}")
                            continue

                except Exception as e:
                    print(f"Warning: Could not extract images from page {page_num}: {e}")
                    images = []
                
                pages_data.append({
                    "page_number": page_num,
                    "text_blocks": text_blocks,
                    "images": images
                })

                print(f"Page {page_num + 1}: Found {len(text_blocks)} text blocks and {len(images)} images")

        except Exception as e:
            print(f"Error extracting elements from {pdf_path}: {e}")
            # Don't raise the exception, return what we have so far
            print(f"Returning {len(pages_data)} pages that were successfully processed")

        finally:
            # Ensure document is closed
            if doc:
                try:
                    doc.close()
                except:
                    pass

        return pages_data
    
    def find_closest_text_to_image(self, image_bbox: List[float], text_blocks: List[Dict]) -> Optional[Dict]:
        """
        Find the closest text block to an image.
        
        Args:
            image_bbox: Bounding box of the image [x0, y0, x1, y1]
            text_blocks: List of text blocks with their bounding boxes
            
        Returns:
            Closest text block or None
        """
        if not text_blocks:
            return None
        
        img_center_x = (image_bbox[0] + image_bbox[2]) / 2
        img_center_y = (image_bbox[1] + image_bbox[3]) / 2
        
        closest_text = None
        min_distance = float('inf')
        
        for text_block in text_blocks:
            text_bbox = text_block["bbox"]
            text_center_x = (text_bbox[0] + text_bbox[2]) / 2
            text_center_y = (text_bbox[1] + text_bbox[3]) / 2
            
            # Calculate Euclidean distance
            distance = ((img_center_x - text_center_x) ** 2 + (img_center_y - text_center_y) ** 2) ** 0.5
            
            if distance < min_distance:
                min_distance = distance
                closest_text = text_block
        
        return closest_text
    
    def group_related_text_blocks(self, text_blocks: List[Dict], proximity_threshold: float = 50.0) -> List[List[Dict]]:
        """
        Group related text blocks based on proximity.
        
        Args:
            text_blocks: List of text blocks
            proximity_threshold: Maximum distance to consider blocks as related
            
        Returns:
            List of grouped text blocks
        """
        if not text_blocks:
            return []
        
        groups = []
        used_blocks = set()
        
        for i, block in enumerate(text_blocks):
            if i in used_blocks:
                continue
            
            current_group = [block]
            used_blocks.add(i)
            
            block_bbox = block["bbox"]
            block_center_y = (block_bbox[1] + block_bbox[3]) / 2
            
            # Find nearby blocks
            for j, other_block in enumerate(text_blocks):
                if j in used_blocks or i == j:
                    continue
                
                other_bbox = other_block["bbox"]
                other_center_y = (other_bbox[1] + other_bbox[3]) / 2
                
                # Check if blocks are close vertically (reading order)
                if abs(block_center_y - other_center_y) <= proximity_threshold:
                    current_group.append(other_block)
                    used_blocks.add(j)
            
            groups.append(current_group)
        
        return groups
    
    def save_image(self, image_data: bytes, pdf_name: str, page_num: int, img_index: int) -> str:
        """
        Save image data to file and return the path.
        
        Args:
            image_data: Raw image data
            pdf_name: Name of the source PDF
            page_num: Page number
            img_index: Image index on the page
            
        Returns:
            Relative path to the saved image
        """
        image_filename = f"{pdf_name}_page_{page_num}_img_{img_index}.png"
        image_path = self.visuals_folder / image_filename
        
        with open(image_path, 'wb') as f:
            f.write(image_data)
        
        return f"./visuals/{image_filename}"
    
    def create_chunks_folder(self, pdf_name: str) -> Path:
        """
        Create a folder for storing visual chunks with the format: chunks_n_name_files
        
        Args:
            pdf_name: Name of the PDF file (without extension)
            
        Returns:
            Path to the created folder
        """
        folder_name = f"chunks_{self._folder_counter}_{pdf_name}_files"
        folder_path = self.staging_folder / folder_name
        folder_path.mkdir(exist_ok=True)
        
        self._folder_counter += 1
        return folder_path

    def create_visual_chunk(self, text_group: List[Dict], associated_images: List[Dict],
                           pdf_name: str, page_num: int, chunk_id: str) -> Dict[str, Any]:
        """
        Create a visual chunk with text and associated images.

        Args:
            text_group: Group of related text blocks
            associated_images: Images associated with this text group
            pdf_name: Name of the source PDF
            page_num: Page number
            chunk_id: Unique identifier for the chunk

        Returns:
            Visual chunk data structure
        """
        # Combine text from all blocks in the group
        combined_text = " ".join([block["text"] for block in text_group])

        # Determine content type
        content_type = "text_with_visuals" if associated_images else "text_only"

        # Process visual citations
        visual_citations = []

        for img in associated_images:
            # Generate unique reference ID
            ref_id = f"visual_ref_{self._visual_ref_counter}"
            self._visual_ref_counter += 1

            # Save image and get path
            image_path = self.save_image(
                img["image_data"],
                pdf_name,
                img["page_num"],
                img["img_index"]
            )

            # Insert reference tag into text
            combined_text += f" [see: {ref_id}]"

            # Create visual citation entry (description will be added later by vision model)
            visual_citations.append({
                "ref_id": ref_id,
                "source_image_path": image_path,
                "description_from_vision_model": None,  # To be filled by vision analysis
                "image_bbox": img["bbox"]
            })

        return {
            "chunk_id": chunk_id,
            "source_document": f"{pdf_name}.pdf",
            "page_number": page_num,
            "content_type": content_type,
            "text": combined_text,
            "visual_citations": visual_citations
        }

    def process_visual_chunks_with_vision_model(self, chunks: List[Dict], vision_analyzer) -> List[Dict]:
        """
        Process visual chunks by analyzing images with the vision model.

        Args:
            chunks: List of visual chunks
            vision_analyzer: VisionAnalyzer instance

        Returns:
            Updated chunks with vision model descriptions
        """
        for chunk in chunks:
            if chunk["content_type"] == "text_with_visuals":
                for citation in chunk["visual_citations"]:
                    if citation["description_from_vision_model"] is None:
                        # Read image file and analyze
                        image_path = citation["source_image_path"]
                        # Convert relative path to absolute
                        abs_image_path = Path(image_path.replace("./visuals/", str(self.visuals_folder) + "/"))

                        if abs_image_path.exists():
                            analysis_result = vision_analyzer.analyze_image_from_file(str(abs_image_path))

                            if analysis_result["status"] == "success":
                                citation["description_from_vision_model"] = analysis_result["analysis"]
                            else:
                                citation["description_from_vision_model"] = f"Error analyzing image: {analysis_result.get('error', 'Unknown error')}"
                        else:
                            citation["description_from_vision_model"] = "Image file not found"

        return chunks

    def save_visual_chunk(self, chunk: Dict[str, Any], chunk_folder: Path, chunk_index: int) -> Path:
        """
        Save a visual chunk as JSON file with the format: chunk_n.json

        Args:
            chunk: The visual chunk data to save
            chunk_folder: Folder where the chunk should be saved
            chunk_index: Index of the chunk

        Returns:
            Path to the saved chunk file
        """
        chunk_filename = f"chunk_{chunk_index}.json"
        chunk_path = chunk_folder / chunk_filename

        with open(chunk_path, 'w', encoding='utf-8') as f:
            json.dump(chunk, f, indent=2, ensure_ascii=False)

        return chunk_path

    def process_pdf_with_visuals(self, pdf_filename: str, vision_analyzer=None) -> Dict[str, Any]:
        """
        Process a single PDF file with visual content extraction.

        Args:
            pdf_filename: Name of the PDF file in the raw folder
            vision_analyzer: VisionAnalyzer instance for image analysis

        Returns:
            Dictionary with processing results
        """
        pdf_path = self.raw_folder / pdf_filename

        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        # Extract PDF name without extension
        pdf_name = pdf_path.stem

        print(f"Processing PDF with visuals: {pdf_filename}")

        # Extract page elements (text blocks and images)
        pages_data = self.extract_page_elements(pdf_path)

        if not pages_data:
            print(f"Warning: No content extracted from {pdf_filename}")
            return {
                "pdf_name": pdf_name,
                "status": "no_content",
                "chunks_created": 0,
                "folder_path": None
            }

        # Check if we have any content at all
        total_text_blocks = sum(len(page["text_blocks"]) for page in pages_data)
        total_images = sum(len(page["images"]) for page in pages_data)

        print(f"Extracted content: {total_text_blocks} text blocks, {total_images} images")

        if total_text_blocks == 0 and total_images == 0:
            print(f"Warning: No text or images found in {pdf_filename}")
            return {
                "pdf_name": pdf_name,
                "status": "no_content",
                "chunks_created": 0,
                "folder_path": None
            }

        # Create folder for chunks
        chunks_folder = self.create_chunks_folder(pdf_name)

        # Process each page and create visual chunks
        all_chunks = []
        chunk_counter = 0

        for page_data in pages_data:
            page_num = page_data["page_number"]
            text_blocks = page_data["text_blocks"]
            images = page_data["images"]

            if not text_blocks and not images:
                continue

            # Group related text blocks
            text_groups = self.group_related_text_blocks(text_blocks)

            # If no text groups but images exist, create chunks for images only
            if not text_groups and images:
                text_groups = [[]]  # Empty text group for image-only chunks

            # Process each text group
            for text_group in text_groups:
                # Find images associated with this text group
                associated_images = []

                if images and text_group:
                    # Find closest images to the text group
                    for img in images:
                        closest_text = self.find_closest_text_to_image(img["bbox"], text_group)
                        if closest_text:
                            associated_images.append(img)
                elif images and not text_group:
                    # If no text, associate all images on the page
                    associated_images = images

                # Create visual chunk
                chunk_id = f"visual_chunk_{chunk_counter}"
                chunk = self.create_visual_chunk(
                    text_group,
                    associated_images,
                    pdf_name,
                    page_num,
                    chunk_id
                )

                all_chunks.append(chunk)
                chunk_counter += 1

        # Process chunks with vision model if provided
        if vision_analyzer and all_chunks:
            print("Analyzing images with vision model...")
            all_chunks = self.process_visual_chunks_with_vision_model(all_chunks, vision_analyzer)

        # Save chunks
        saved_chunks = []
        for i, chunk in enumerate(all_chunks):
            chunk_path = self.save_visual_chunk(chunk, chunks_folder, i)
            saved_chunks.append(str(chunk_path))

        result = {
            "pdf_name": pdf_name,
            "status": "success",
            "chunks_created": len(all_chunks),
            "folder_path": str(chunks_folder),
            "chunk_files": saved_chunks
        }

        print(f"Successfully processed {pdf_filename}: {len(all_chunks)} visual chunks created in {chunks_folder}")
        return result

    def process_all_pdfs_with_visuals(self, vision_analyzer=None) -> List[Dict[str, Any]]:
        """
        Process all PDF files in the raw folder with visual content extraction.

        Args:
            vision_analyzer: VisionAnalyzer instance for image analysis

        Returns:
            List of processing results for each PDF
        """
        pdf_files = [f for f in self.raw_folder.iterdir() if f.suffix.lower() == '.pdf']

        if not pdf_files:
            print("No PDF files found in the subprocess raw folder.")
            return []

        results = []
        for pdf_file in pdf_files:
            try:
                result = self.process_pdf_with_visuals(pdf_file.name, vision_analyzer)
                results.append(result)
            except Exception as e:
                print(f"Error processing {pdf_file.name}: {e}")
                results.append({
                    "pdf_name": pdf_file.stem,
                    "status": "error",
                    "error": str(e),
                    "chunks_created": 0,
                    "folder_path": None
                })

        return results
